import { supabase } from '../integrations/supabase/client';

/**
 * Product Analytics Service for YalaOffice
 *
 * Database Indexing Recommendations for Optimal Performance:
 *
 * 1. order_items table:
 *    - CREATE INDEX idx_order_items_product_created ON order_items(product_id, created_at);
 *    - CREATE INDEX idx_order_items_orders_status ON orders(status, created_at);
 *
 * 2. cart_items table:
 *    - CREATE INDEX idx_cart_items_product_created ON cart_items(product_id, created_at);
 *
 * 3. wishlist_items table:
 *    - CREATE INDEX idx_wishlist_items_product_created ON wishlist_items(product_id, created_at);
 *
 * 4. product_views table (if implemented):
 *    - CREATE INDEX idx_product_views_product_created ON product_views(product_id, created_at);
 *
 * 5. products table:
 *    - CREATE INDEX idx_products_active_created ON products(is_active, created_at);
 */

// Weights for different user activities (higher = more important)
const ACTIVITY_WEIGHTS = {
  COMPLETED_ORDER: 10,    // Highest weight - actual purchase
  CART_ADDITION: 8,       // High weight - strong purchase intent
  PRODUCT_VIEW: 3,        // Medium weight - interest shown
  WISHLIST_ADDITION: 2    // Lower weight - future interest
};

// Time windows for analytics (in days)
const TIME_WINDOWS = [30, 60, 90];

// Cache for popularity scores to avoid recalculation
interface CacheEntry {
  scores: ProductPopularityScore[];
  timestamp: number;
  timeWindow: number;
}

const popularityCache = new Map<string, CacheEntry>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export interface ProductPopularityScore {
  productId: string;
  popularityScore: number;
  orderCount: number;
  cartAdditions: number;
  productViews: number;
  wishlistAdditions: number;
  lastActivity: string;
}

export interface AnalyticsTimeWindow {
  days: number;
  startDate: string;
  endDate: string;
}

/**
 * Get cached popularity scores if available and not expired
 */
const getCachedScores = (timeWindow: number): ProductPopularityScore[] | null => {
  const cacheKey = `scores_${timeWindow}`;
  const cached = popularityCache.get(cacheKey);

  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    console.log(`Using cached popularity scores for ${timeWindow}-day window`);
    return cached.scores;
  }

  return null;
};

/**
 * Cache popularity scores for future use
 */
const setCachedScores = (timeWindow: number, scores: ProductPopularityScore[]): void => {
  const cacheKey = `scores_${timeWindow}`;
  popularityCache.set(cacheKey, {
    scores,
    timestamp: Date.now(),
    timeWindow
  });
  console.log(`Cached popularity scores for ${timeWindow}-day window`);
};

/**
 * Clear cache when data changes occur
 */
export const clearPopularityCache = (): void => {
  popularityCache.clear();
  console.log('Popularity cache cleared');
};

/**
 * Calculate popularity score for a product based on user activities
 */
const calculatePopularityScore = (metrics: {
  orderCount: number;
  cartAdditions: number;
  productViews: number;
  wishlistAdditions: number;
}): number => {
  const { orderCount, cartAdditions, productViews, wishlistAdditions } = metrics;
  
  return (
    (orderCount * ACTIVITY_WEIGHTS.COMPLETED_ORDER) +
    (cartAdditions * ACTIVITY_WEIGHTS.CART_ADDITION) +
    (productViews * ACTIVITY_WEIGHTS.PRODUCT_VIEW) +
    (wishlistAdditions * ACTIVITY_WEIGHTS.WISHLIST_ADDITION)
  );
};

/**
 * Get time window for analytics queries
 */
const getTimeWindow = (days: number): AnalyticsTimeWindow => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return {
    days,
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString()
  };
};

/**
 * Get order-based metrics for products
 */
const getOrderMetrics = async (timeWindow: AnalyticsTimeWindow): Promise<Map<string, number>> => {
  try {
    console.log(`Fetching order metrics for last ${timeWindow.days} days...`);
    
    const { data: orderItems, error } = await supabase
      .from('order_items')
      .select(`
        product_id,
        quantity,
        orders!inner(
          created_at,
          status
        )
      `)
      .gte('orders.created_at', timeWindow.startDate)
      .lte('orders.created_at', timeWindow.endDate)
      .eq('orders.status', 'completed');

    if (error) {
      console.error('Error fetching order metrics:', error);
      return new Map();
    }

    const orderCounts = new Map<string, number>();
    
    orderItems?.forEach(item => {
      const productId = item.product_id;
      const currentCount = orderCounts.get(productId) || 0;
      orderCounts.set(productId, currentCount + (item.quantity || 1));
    });

    console.log(`Found order data for ${orderCounts.size} products`);
    return orderCounts;
    
  } catch (error) {
    console.error('Error in getOrderMetrics:', error);
    return new Map();
  }
};

/**
 * Get cart addition metrics for products
 * Note: This assumes cart data is stored in a cart_items table
 */
const getCartMetrics = async (timeWindow: AnalyticsTimeWindow): Promise<Map<string, number>> => {
  try {
    console.log(`Fetching cart metrics for last ${timeWindow.days} days...`);
    
    // Check if cart_items table exists and has the required structure
    const { data: cartItems, error } = await supabase
      .from('cart_items')
      .select('product_id, created_at')
      .gte('created_at', timeWindow.startDate)
      .lte('created_at', timeWindow.endDate);

    if (error) {
      console.log('Cart metrics not available (table may not exist):', error.message);
      return new Map();
    }

    const cartCounts = new Map<string, number>();
    
    cartItems?.forEach(item => {
      const productId = item.product_id;
      const currentCount = cartCounts.get(productId) || 0;
      cartCounts.set(productId, currentCount + 1);
    });

    console.log(`Found cart data for ${cartCounts.size} products`);
    return cartCounts;
    
  } catch (error) {
    console.log('Cart metrics not available:', error);
    return new Map();
  }
};

/**
 * Get wishlist addition metrics for products
 */
const getWishlistMetrics = async (timeWindow: AnalyticsTimeWindow): Promise<Map<string, number>> => {
  try {
    console.log(`Fetching wishlist metrics for last ${timeWindow.days} days...`);
    
    const { data: wishlistItems, error } = await supabase
      .from('wishlist_items')
      .select('product_id, created_at')
      .gte('created_at', timeWindow.startDate)
      .lte('created_at', timeWindow.endDate);

    if (error) {
      console.log('Wishlist metrics not available:', error.message);
      return new Map();
    }

    const wishlistCounts = new Map<string, number>();
    
    wishlistItems?.forEach(item => {
      const productId = item.product_id;
      const currentCount = wishlistCounts.get(productId) || 0;
      wishlistCounts.set(productId, currentCount + 1);
    });

    console.log(`Found wishlist data for ${wishlistCounts.size} products`);
    return wishlistCounts;
    
  } catch (error) {
    console.log('Wishlist metrics not available:', error);
    return new Map();
  }
};

/**
 * Get product view metrics (placeholder - would need actual view tracking)
 */
const getProductViewMetrics = async (timeWindow: AnalyticsTimeWindow): Promise<Map<string, number>> => {
  try {
    console.log(`Fetching product view metrics for last ${timeWindow.days} days...`);
    
    // This would require a product_views or analytics table
    // For now, return empty map as this data may not be available
    const { data: productViews, error } = await supabase
      .from('product_views')
      .select('product_id, created_at')
      .gte('created_at', timeWindow.startDate)
      .lte('created_at', timeWindow.endDate);

    if (error) {
      console.log('Product view metrics not available:', error.message);
      return new Map();
    }

    const viewCounts = new Map<string, number>();
    
    productViews?.forEach(view => {
      const productId = view.product_id;
      const currentCount = viewCounts.get(productId) || 0;
      viewCounts.set(productId, currentCount + 1);
    });

    console.log(`Found view data for ${viewCounts.size} products`);
    return viewCounts;
    
  } catch (error) {
    console.log('Product view metrics not available:', error);
    return new Map();
  }
};

/**
 * Calculate popularity scores for all products within a time window
 */
export const calculateProductPopularityScores = async (
  timeWindow: AnalyticsTimeWindow
): Promise<ProductPopularityScore[]> => {
  try {
    console.log(`Calculating popularity scores for ${timeWindow.days}-day window...`);

    // Check cache first
    const cachedScores = getCachedScores(timeWindow.days);
    if (cachedScores) {
      return cachedScores;
    }

    // Fetch all metrics in parallel
    const [orderMetrics, cartMetrics, wishlistMetrics, viewMetrics] = await Promise.all([
      getOrderMetrics(timeWindow),
      getCartMetrics(timeWindow),
      getWishlistMetrics(timeWindow),
      getProductViewMetrics(timeWindow)
    ]);

    // Combine all product IDs from all metrics
    const allProductIds = new Set<string>();
    [orderMetrics, cartMetrics, wishlistMetrics, viewMetrics].forEach(metrics => {
      metrics.forEach((_, productId) => allProductIds.add(productId));
    });

    console.log(`Found activity for ${allProductIds.size} unique products`);

    // Calculate popularity scores
    const popularityScores: ProductPopularityScore[] = Array.from(allProductIds).map(productId => {
      const orderCount = orderMetrics.get(productId) || 0;
      const cartAdditions = cartMetrics.get(productId) || 0;
      const productViews = viewMetrics.get(productId) || 0;
      const wishlistAdditions = wishlistMetrics.get(productId) || 0;

      const popularityScore = calculatePopularityScore({
        orderCount,
        cartAdditions,
        productViews,
        wishlistAdditions
      });

      return {
        productId,
        popularityScore,
        orderCount,
        cartAdditions,
        productViews,
        wishlistAdditions,
        lastActivity: timeWindow.endDate
      };
    });

    // Sort by popularity score (highest first)
    popularityScores.sort((a, b) => b.popularityScore - a.popularityScore);

    console.log(`Calculated popularity scores for ${popularityScores.length} products`);

    // Cache the results
    setCachedScores(timeWindow.days, popularityScores);

    return popularityScores;
    
  } catch (error) {
    console.error('Error calculating popularity scores:', error);
    return [];
  }
};

/**
 * Incrementally update popularity scores for a specific product
 */
export const updateProductPopularityScore = async (
  productId: string,
  activityType: 'order' | 'cart' | 'wishlist' | 'view',
  increment: number = 1
): Promise<void> => {
  try {
    console.log(`Updating popularity score for product ${productId}: ${activityType} +${increment}`);

    // For now, we'll clear the cache to force recalculation
    // In a production system, you might want to implement more sophisticated incremental updates
    clearPopularityCache();

    console.log(`Popularity cache cleared for product ${productId} activity: ${activityType}`);
  } catch (error) {
    console.error('Error updating product popularity score:', error);
  }
};

/**
 * Batch update popularity scores for multiple products
 */
export const batchUpdatePopularityScores = async (
  updates: Array<{
    productId: string;
    activityType: 'order' | 'cart' | 'wishlist' | 'view';
    increment: number;
  }>
): Promise<void> => {
  try {
    console.log(`Batch updating popularity scores for ${updates.length} products`);

    // Clear cache once for all updates
    clearPopularityCache();

    console.log('Popularity cache cleared for batch update');
  } catch (error) {
    console.error('Error batch updating popularity scores:', error);
  }
};

/**
 * Get best selling products based on analytics with fallback time windows
 */
export const getAnalyticsBasedBestSellingProducts = async (limit: number = 4): Promise<string[]> => {
  try {
    console.log('Getting analytics-based best selling products...');
    
    // Try different time windows until we find sufficient data
    for (const days of TIME_WINDOWS) {
      const timeWindow = getTimeWindow(days);
      const popularityScores = await calculateProductPopularityScores(timeWindow);
      
      if (popularityScores.length >= limit) {
        const topProductIds = popularityScores
          .slice(0, limit)
          .map(score => score.productId);
        
        console.log(`Found ${topProductIds.length} products using ${days}-day window`);
        return topProductIds;
      }
      
      console.log(`Insufficient data in ${days}-day window (${popularityScores.length} products), trying longer period...`);
    }
    
    // If no analytics data found, return empty array (will trigger fallback to newest products)
    console.log('No sufficient analytics data found in any time window');
    return [];
    
  } catch (error) {
    console.error('Error getting analytics-based best selling products:', error);
    return [];
  }
};

/**
 * Force recalculation of analytics (useful for testing or manual refresh)
 */
export const forceAnalyticsRecalculation = async (): Promise<void> => {
  try {
    console.log('Forcing analytics recalculation...');
    clearPopularityCache();

    // Trigger a fresh calculation for the default 30-day window
    const timeWindow = getTimeWindow(30);
    await calculateProductPopularityScores(timeWindow);

    console.log('Analytics recalculation completed');
  } catch (error) {
    console.error('Error forcing analytics recalculation:', error);
  }
};

/**
 * Get analytics summary for debugging
 */
export const getAnalyticsSummary = async (): Promise<{
  cacheSize: number;
  timeWindows: number[];
  lastCalculation: string | null;
}> => {
  return {
    cacheSize: popularityCache.size,
    timeWindows: TIME_WINDOWS,
    lastCalculation: popularityCache.size > 0 ? new Date().toISOString() : null
  };
};
