import { useState, useEffect } from 'react';
import { TrendingUp, Star, ShoppingCart, Package, Eye, ArrowUp, ArrowDown, Heart } from 'lucide-react';
import { getBestSellingProducts, BestSellingProduct } from '../../services/analyticsService';
import { clearPopularityCache } from '../../services/productAnalyticsService';
import { realTimeService } from '../../services/realTimeService';
import { liveDataService } from '../../services/liveDataService';
import { getActiveCategories } from '../../services/liveCategoryService';
import ProductDetailsModal from '../products/ProductDetailsModal';
import { formatCurrency } from '../../utils/currency';
import { useWishlist } from '../../hooks/useWishlist';

interface BestSellingProductsProps {
  userType: 'client' | 'reseller';
  customerId?: string;
  customerName?: string;
  onAddToCart?: (product: any) => void;
  onViewProduct?: (productId: string) => void;
  onAddToWishlist?: (product: any) => void;
}

const BestSellingProducts = ({ userType, customerId, customerName, onAddToCart, onViewProduct, onAddToWishlist }: BestSellingProductsProps) => {
  const [bestSellingProducts, setBestSellingProducts] = useState<BestSellingProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [showProductModal, setShowProductModal] = useState(false);
  const [addingToCart, setAddingToCart] = useState<string | null>(null);
  const [addingToWishlist, setAddingToWishlist] = useState<string | null>(null);

  // Use wishlist hook if customerId is provided
  const wishlist = customerId ? useWishlist(customerId) : null;

  useEffect(() => {
    loadProductData();

    // Subscribe to real-time updates
    const unsubscribeProduct = realTimeService.subscribe('product-updated', handleProductUpdate);
    const unsubscribeStock = realTimeService.subscribe('stock-updated', handleStockUpdate);
    const unsubscribePrice = realTimeService.subscribe('price-updated', handlePriceUpdate);
    const unsubscribeOrder = realTimeService.subscribe('order-updated', handleOrderUpdate);

    // Subscribe to Supabase real-time updates for products
    const productSubscription = liveDataService.subscribeToProducts((payload) => {
      console.log('Real-time product update in BestSellingProducts:', payload);

      // Handle different types of product updates
      if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
        // Reload all product data to ensure consistency
        loadProductData();
      } else if (payload.eventType === 'DELETE') {
        // Remove deleted product from current list
        const deletedProductId = payload.old?.id;
        if (deletedProductId) {
          setBestSellingProducts(prev =>
            prev.filter(product => product.id !== deletedProductId)
          );
        }
      }
    });

    const orderSubscription = liveDataService.subscribeToOrders((payload) => {
      console.log('Real-time order update in BestSellingProducts:', payload);

      // Clear analytics cache when orders change (affects popularity scores)
      clearPopularityCache();

      // Reload best-selling products when orders change to reflect new sales data
      loadBestSellingProducts().then(data => {
        setBestSellingProducts(data);
        console.log('Best selling products updated after order change');
      }).catch(error => {
        console.error('Error updating best selling products after order change:', error);
      });
    });

    // Subscribe to cart and wishlist changes (affects analytics)
    const cartSubscription = liveDataService.subscribeToTable('cart_items', (payload) => {
      console.log('Real-time cart update in BestSellingProducts:', payload);
      // Clear cache when cart activity changes (affects popularity scores)
      clearPopularityCache();
    });

    const wishlistSubscription = liveDataService.subscribeToTable('wishlist_items', (payload) => {
      console.log('Real-time wishlist update in BestSellingProducts:', payload);
      // Clear cache when wishlist activity changes (affects popularity scores)
      clearPopularityCache();
    });

    return () => {
      unsubscribeProduct();
      unsubscribeStock();
      unsubscribePrice();
      unsubscribeOrder();
      productSubscription.unsubscribe();
      orderSubscription.unsubscribe();
      if (cartSubscription) cartSubscription.unsubscribe();
      if (wishlistSubscription) wishlistSubscription.unsubscribe();
    };
  }, []);

  const loadProductData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load only best-selling products with real-time data
      const bestSellingData = await loadBestSellingProducts();

      if (bestSellingData.length === 0) {
        console.warn('No best-selling products found');
      }

      setBestSellingProducts(bestSellingData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load product data';
      setError(errorMessage);
      console.error('Error loading product data:', err);

      // Set empty array on error to prevent UI issues
      setBestSellingProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const loadBestSellingProducts = async (): Promise<BestSellingProduct[]> => {
    try {
      console.log('Loading best selling products...');
      const data = await getBestSellingProducts(4);
      console.log('Best selling products loaded:', data);

      // Debug: Log each product's key data
      data.forEach((product, index) => {
        console.log(`Product ${index + 1}:`, {
          id: product.id,
          title: product.title,
          price: product.price,
          resellerPrice: product.resellerPrice,
          image: product.image,
          rating: product.rating,
          category: product.category
        });
      });

      return data;
    } catch (err) {
      console.error('Error loading best selling products:', err);
      return [];
    }
  };



  // Real-time event handlers with enhanced error handling
  const handleProductUpdate = (event: any) => {
    try {
      const { productId, newData } = event.data || {};
      if (!productId || !newData) return;

      setBestSellingProducts(prev => prev.map(product =>
        product.id === productId ? { ...product, ...newData } : product
      ));
      console.log(`Updated product ${productId} in best selling products`);
    } catch (error) {
      console.error('Error handling product update:', error);
    }
  };

  const handleStockUpdate = (event: any) => {
    // Stock updates are no longer relevant since we removed stock logic
    console.log('Stock update received but ignored (stock logic removed):', event);
  };

  const handlePriceUpdate = (event: any) => {
    try {
      const { productId, newPrice, newResellerPrice } = event.data || {};
      if (!productId) return;

      setBestSellingProducts(prev => prev.map(product =>
        product.id === productId ? {
          ...product,
          price: newPrice !== undefined ? newPrice : product.price,
          resellerPrice: newResellerPrice !== undefined ? newResellerPrice : product.resellerPrice
        } : product
      ));
      console.log(`Updated prices for product ${productId}`);
    } catch (error) {
      console.error('Error handling price update:', error);
    }
  };

  const handleOrderUpdate = (event: any) => {
    try {
      // When orders change, reload best-selling products to reflect new sales data
      console.log('Order update detected, refreshing best selling products...');
      loadBestSellingProducts().then(data => {
        setBestSellingProducts(data);
        console.log('Best selling products refreshed after order update');
      }).catch(error => {
        console.error('Error refreshing best selling products after order update:', error);
      });
    } catch (error) {
      console.error('Error handling order update:', error);
    }
  };

  const handleAddToCart = async (product: any) => {
    if (!onAddToCart) return;

    try {
      setAddingToCart(product.id);

      const cartProduct = {
        id: product.id,
        title: product.title,
        category: product.category,
        price: userType === 'reseller' ? product.resellerPrice : product.price,
        resellerPrice: product.resellerPrice,
        image: product.featuredImage || product.image,
        featuredImage: product.featuredImage || product.image,
        rating: product.rating,
        quantity: 1
      };

      await onAddToCart(cartProduct);

      // Show success feedback (could be enhanced with toast notifications)
      console.log(`Added ${product.title} to cart successfully`);

    } catch (error) {
      console.error('Error adding to cart:', error);
      setError('Failed to add product to cart');
    } finally {
      setAddingToCart(null);
    }
  };

  const handleAddToWishlist = async (product: any) => {
    if (!customerId) {
      console.warn('No customer ID provided for wishlist operation');
      return;
    }

    try {
      setAddingToWishlist(product.id);

      if (wishlist) {
        const isWishlisted = wishlist.isInWishlist(product.id);
        if (isWishlisted) {
          await wishlist.removeFromWishlist(product.id);
          console.log(`Removed ${product.title} from wishlist`);
        } else {
          await wishlist.addToWishlist(product.id);
          console.log(`Added ${product.title} to wishlist`);
        }
      } else if (onAddToWishlist) {
        // Fallback to callback if no wishlist hook
        onAddToWishlist(product);
      }
    } catch (error) {
      console.error('Error updating wishlist:', error);
      setError('Failed to update wishlist');
    } finally {
      setAddingToWishlist(null);
    }
  };

  const handleViewProduct = (productId: string) => {
    setSelectedProductId(productId);
    setShowProductModal(true);

    // Also call the optional callback
    if (onViewProduct) {
      onViewProduct(productId);
    }
  };

  const handleCloseModal = () => {
    setShowProductModal(false);
    setSelectedProductId(null);
  };



  const formatPrice = (price: number) => {
    return formatCurrency(price);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-32 mb-3"></div>
              <div className="bg-gray-200 rounded h-4 mb-2"></div>
              <div className="bg-gray-200 rounded h-3 w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        <div className="text-center py-8">
          <div className="bg-red-100 text-red-700 p-4 rounded-lg mb-4 inline-block">
            <p className="font-medium">Failed to load products</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
          <br />
          <button
            onClick={loadProductData}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (bestSellingProducts.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        <div className="text-center py-8">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 font-medium">No best selling products found</p>
          <p className="text-sm text-gray-500 mt-1">Products will appear here once orders are placed</p>
          <button
            onClick={loadProductData}
            className="mt-4 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-3 sm:space-y-0">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-lg sm:text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-xs sm:text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        {userType === 'reseller' && (
          <div className="bg-gradient-to-r from-teal-50 to-teal-100 px-3 py-1 rounded-full self-start sm:self-auto">
            <span className="text-xs sm:text-sm font-medium text-teal-700">Reseller Pricing</span>
          </div>
        )}
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {bestSellingProducts.map((product, index) => {
          const displayPrice = userType === 'reseller' ? product.resellerPrice : product.price;
          const savings = userType === 'reseller' ? product.price - product.resellerPrice : 0;
          
          return (
            <div
              key={product.id}
              className="group relative bg-gradient-to-br from-gray-50 to-white border border-gray-200 rounded-xl p-4 hover:shadow-lg hover:border-orange-200 transition-all duration-300 hover:-translate-y-1"
            >
              {/* Rank Badge */}
              <div className="absolute -top-2 -left-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs font-bold w-6 h-6 rounded-full flex items-center justify-center shadow-lg">
                {index + 1}
              </div>

              {/* Growth Indicator */}
              <div className="absolute top-2 right-2 flex items-center space-x-1">
                {product.growth > 0 ? (
                  <ArrowUp className="h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDown className="h-3 w-3 text-red-500" />
                )}
                <span className={`text-xs font-medium ${product.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(product.growth || 0).toFixed(1)}%
                </span>
              </div>

              {/* Product Image */}
              <div className="bg-gray-100 rounded-lg h-24 mb-3 flex items-center justify-center group-hover:bg-gray-200 transition-colors overflow-hidden">
                {product.image && product.image !== '/placeholder.svg' ? (
                  <img
                    src={product.featuredImage || product.image}
                    alt={product.title}
                    className="w-full h-full object-cover rounded-lg"
                    onError={(e) => {
                      // Fallback to icon if image fails to load
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : null}
                <Package className={`h-8 w-8 text-gray-400 ${product.image && product.image !== '/placeholder.svg' ? 'hidden' : ''}`} />
              </div>

              {/* Product Info */}
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-900 text-sm line-clamp-2 group-hover:text-orange-600 transition-colors">
                  {product.title}
                </h4>
                
                <div className="flex items-center space-x-1">
                  <Star className="h-3 w-3 text-amber-500 fill-current" />
                  <span className="text-xs text-gray-600">{Number(product.rating).toFixed(1)}</span>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-gray-900">
                      {formatPrice(displayPrice)}
                    </span>
                    {userType === 'reseller' && savings > 0 && (
                      <span className="text-xs text-gray-500 line-through">
                        {formatPrice(product.price)}
                      </span>
                    )}
                  </div>

                  {userType === 'reseller' && savings > 0 && (
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-green-600 font-medium">
                        Save {formatPrice(savings)}
                      </div>
                      <div className="text-xs bg-teal-100 text-teal-700 px-2 py-1 rounded-full font-medium">
                        Reseller Price
                      </div>
                    </div>
                  )}

                  {userType === 'reseller' && savings === 0 && (
                    <div className="text-xs bg-teal-100 text-teal-700 px-2 py-1 rounded-full font-medium inline-block">
                      Reseller Price
                    </div>
                  )}
                </div>

                {/* Sales Stats */}
                <div className="flex items-center justify-center text-xs text-gray-500">
                  <span>{formatNumber(product.unitsSold || 0)} sold</span>
                </div>

                {/* Action Buttons - Three buttons in a row */}
                <div className="flex space-x-1 pt-2">
                  <button
                    onClick={() => handleAddToCart(product)}
                    disabled={addingToCart === product.id}
                    className={`flex-1 px-2 py-2 rounded-lg text-xs font-medium transition-all flex items-center justify-center space-x-1 ${
                      addingToCart !== product.id
                        ? 'bg-gradient-to-r from-teal-500 to-teal-600 text-white hover:from-teal-600 hover:to-teal-700'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {addingToCart === product.id ? (
                      <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <ShoppingCart className="h-3 w-3" />
                    )}
                    <span>{addingToCart === product.id ? 'Adding...' : 'Cart'}</span>
                  </button>

                  <button
                    onClick={() => handleViewProduct(product.id)}
                    className="px-2 py-2 bg-gray-100 text-gray-700 rounded-lg text-xs font-medium hover:bg-gray-200 transition-colors flex items-center justify-center"
                    title="View Details"
                    aria-label="View product details"
                  >
                    <Eye className="h-3 w-3" />
                  </button>

                  <button
                    onClick={() => handleAddToWishlist(product)}
                    disabled={addingToWishlist === product.id}
                    className={`px-2 py-2 rounded-lg text-xs font-medium transition-colors flex items-center justify-center ${
                      addingToWishlist === product.id
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : wishlist && wishlist.isInWishlist(product.id)
                        ? 'bg-red-100 text-red-700 hover:bg-red-200'
                        : 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                    }`}
                    title={
                      addingToWishlist === product.id
                        ? "Updating wishlist..."
                        : wishlist && wishlist.isInWishlist(product.id)
                        ? "Remove from Wishlist"
                        : "Add to Wishlist"
                    }
                    aria-label={
                      addingToWishlist === product.id
                        ? "Updating wishlist"
                        : wishlist && wishlist.isInWishlist(product.id)
                        ? "Remove from wishlist"
                        : "Add to wishlist"
                    }
                  >
                    {addingToWishlist === product.id ? (
                      <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Heart className={`h-3 w-3 ${wishlist && wishlist.isInWishlist(product.id) ? 'fill-current' : ''}`} />
                    )}
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>



      {/* Product Details Modal */}
      <ProductDetailsModal
        productId={selectedProductId}
        isOpen={showProductModal}
        onClose={handleCloseModal}
        userType={userType}
        customerId={customerId}
        customerName={customerName}
        onAddToCart={onAddToCart}
      />
    </div>
  );
};

export default BestSellingProducts;
